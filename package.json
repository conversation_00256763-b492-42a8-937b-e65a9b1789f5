{"name": "raycast-ai-openrouter-proxy", "version": "0.0.1", "description": "", "main": "src/index.ts", "scripts": {"start": "node dist/index.js", "dev": "tsx watch src/index.ts", "build": "tsc", "lint": "eslint . --max-warnings 0", "lint:fix": "npm run lint -- --fix", "db:up": "docker compose up -d", "db:down": "docker compose down"}, "dependencies": {"dotenv": "^16.5.0", "express": "^5.1.0", "openai": "^5.0.1", "pino": "^9.7.0", "pino-http": "^10.4.0", "zod": "^3.25.42"}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/express": "^5.0.2", "@types/node": "^22.15.28", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.1", "prettier": "^3.5.3", "tsx": "^4.19.4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.0"}, "engines": {"node": ">=20"}}